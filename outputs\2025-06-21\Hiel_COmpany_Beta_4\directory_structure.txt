FINANCIAL MODEL OUTPUT DIRECTORY STRUCTURE
==================================================

Created: 2025-06-21 09:28:05

MAIN DIRECTORIES:
--------------------
main_dir       : Hiel_COmpany_Beta_4/
data_dir       : data/
charts_dir     : charts/
reports_dir    : reports/
exports_dir    : exports/
temp_dir       : temp/

DIRECTORY PURPOSES:
--------------------
main_dir    : Root output directory
data_dir    : Raw and processed data files
charts_dir  : Generated charts and graphs
reports_dir : Final reports (Excel, PDF, HTML)
exports_dir : Exported data files
temp_dir    : Temporary files (auto-cleanup)

SUBDIRECTORY STRUCTURE:
-------------------------
charts/
  ├── financial/     # KPI and financial charts
  ├── analysis/      # Sensitivity and scenario charts
  └── comparison/    # Location and benchmark charts

data/
  ├── raw/           # Original input data
  ├── processed/     # Calculated results
  └── backup/        # Data backups

reports/
  ├── excel/         # Excel workbooks
  ├── pdf/           # PDF reports
  └── html/          # HTML reports

FILE NAMING CONVENTIONS:
-------------------------
• All files include timestamp: YYYYMMDD_HHMMSS
• Client name included when applicable
• Descriptive names with underscores
• No spaces or special characters

CLEANUP POLICY:
---------------
• Temp files: Auto-cleanup after 24 hours
• Backup files: Retain for 30 days
• Reports: Permanent retention
• Charts: Permanent retention
