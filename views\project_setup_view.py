"""
Project Setup View
==================

View component for project setup and configuration.
"""

import flet as ft
import logging
import time
from typing import Dict, Any, Optional

from views.base_view import BaseView
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from components.forms.client_profile_form import ClientProfileForm
from components.forms.project_params_form import ProjectParamsForm


class ProjectSetupView(BaseView):
    """View for project setup and configuration."""

    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.client_profile = ClientProfile()
        self.project_assumptions = EnhancedProjectAssumptions()

        # Initialize logger for this view
        self.history_logger = logging.getLogger(f"{__name__}.ProjectSetupView.History")
        self.history_logger.setLevel(logging.DEBUG)

        # Form components
        self.client_form: Optional[ClientProfileForm] = None
        self.params_form: Optional[ProjectParamsForm] = None
    
    def build_content(self) -> ft.Control:
        """Build the project setup view content."""
        
        # Header
        header = self.create_section_header(
            "Project Setup",
            "Configure client information and project parameters"
        )
        
        # Client Profile Form
        self.client_form = ClientProfileForm(self.client_profile)
        self.client_form.on_data_changed = self._on_client_data_changed
        
        client_card = self.create_card(
            "Client Profile",
            self.client_form.build(),
            icon=ft.Icons.BUSINESS,
            bgcolor=ft.Colors.BLUE_50
        )
        
        # Project Parameters Form
        self.params_form = ProjectParamsForm(self.project_assumptions)
        self.params_form.on_data_changed = self._on_params_data_changed
        
        params_card = self.create_card(
            "Project Parameters",
            self.params_form.build(),
            icon=ft.Icons.SETTINGS
        )
        
        # Action buttons
        action_buttons = self._create_action_buttons()
        
        # Comprehensive report button
        comprehensive_button = self._create_comprehensive_button()
        
        # Validation status
        validation_status = self._create_validation_status()
        
        return ft.Column([
            header,
            client_card,
            params_card,
            validation_status,
            action_buttons,
            comprehensive_button
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_action_buttons(self) -> ft.Row:
        """Create action buttons row."""
        return ft.Row([
            self.create_action_button(
                "Load Preset",
                ft.Icons.UPLOAD_FILE,
                self._on_load_preset,
                ft.Colors.BLUE_600
            ),
            self.create_action_button(
                "History",
                ft.Icons.HISTORY,
                self._on_show_history,
                ft.Colors.PURPLE_600
            ),
            self.create_action_button(
                "Save Configuration",
                ft.Icons.SAVE,
                self._on_save_config,
                ft.Colors.GREEN_600
            ),
            self.create_action_button(
                "Run Model",
                ft.Icons.PLAY_ARROW,
                self._on_run_model,
                ft.Colors.ORANGE_600
            )
        ], alignment=ft.MainAxisAlignment.CENTER)
    
    def _create_comprehensive_button(self) -> ft.Card:
        """Create comprehensive analysis button."""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.AUTO_AWESOME, color=ft.Colors.PURPLE_600),
                        ft.Text("Complete Analysis & Report Generation", 
                               size=18, weight=ft.FontWeight.BOLD)
                    ]),
                    ft.Text(
                        "Generate comprehensive analysis including location comparison, "
                        "all charts, and professional reports",
                        size=12, color=ft.Colors.GREY_700
                    ),
                    ft.ElevatedButton(
                        "🚀 Generate Complete Analysis & Reports",
                        icon=ft.Icons.ROCKET_LAUNCH,
                        on_click=self._on_comprehensive_analysis,
                        bgcolor=ft.Colors.PURPLE_600,
                        color=ft.Colors.WHITE,
                        width=400,
                        height=50,
                        style=ft.ButtonStyle(
                            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.BOLD)
                        )
                    )
                ]),
                padding=20,
                bgcolor=ft.Colors.PURPLE_50
            )
        )
    
    def _create_validation_status(self) -> ft.Container:
        """Create validation status display."""
        validation_results = self.project_assumptions.get_validation_status()
        
        if validation_results['is_valid']:
            status_color = ft.Colors.GREEN
            status_icon = ft.Icons.CHECK_CIRCLE
            status_text = "Configuration Valid"
        else:
            status_color = ft.Colors.RED
            status_icon = ft.Icons.ERROR
            status_text = f"Validation Issues ({validation_results['error_count']} errors)"
        
        status_content = ft.Row([
            ft.Icon(status_icon, color=status_color),
            ft.Text(status_text, color=status_color, weight=ft.FontWeight.BOLD)
        ])
        
        # Add error details if any
        if not validation_results['is_valid']:
            error_details = ft.Column([
                ft.Text("Validation Errors:", size=14, weight=ft.FontWeight.BOLD),
                *[ft.Text(f"• {error}", size=12, color=ft.Colors.RED_700) 
                  for error in validation_results['errors'].values()]
            ])
            
            status_content = ft.Column([
                status_content,
                ft.Divider(height=10),
                error_details
            ])
        
        # Add warnings if any
        warnings = validation_results.get('warnings', {})
        if warnings:
            warning_details = ft.Column([
                ft.Text("Warnings:", size=14, weight=ft.FontWeight.BOLD),
                *[ft.Text(f"• {warning}", size=12, color=ft.Colors.ORANGE_700) 
                  for warning in warnings.values()]
            ])
            
            if isinstance(status_content, ft.Row):
                status_content = ft.Column([status_content])
            
            status_content.controls.extend([
                ft.Divider(height=10),
                warning_details
            ])
        
        return ft.Container(
            content=status_content,
            padding=15,
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            border=ft.border.all(1, status_color)
        )
    
    def _on_client_data_changed(self, field: str, value: Any):
        """Handle client profile data changes."""
        setattr(self.client_profile, field, value)
        self.notify_data_changed("client_profile", self.client_profile)
        self.refresh()  # Refresh to update validation status
    
    def _on_params_data_changed(self, field: str, value: Any):
        """Handle project parameters data changes with auto-save."""
        
        # Get enhanced integration service for auto-save and undo/redo
        try:
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()
            
            # Record undo command if service available
            if enhanced_service.undo_redo_service:
                from services.undo_redo_service import StateChangeCommand
                old_value = getattr(self.project_assumptions, field, None)
                command = StateChangeCommand(
                    target=self.project_assumptions,
                    property_name=field,
                    old_value=old_value,
                    new_value=value,
                    description=f"Change {field} from {old_value} to {value}"
                )
                enhanced_service.undo_redo_service.execute_command(command)
            
        except Exception as e:
            # Fallback to standard behavior if enhanced service unavailable
            print(f"Enhanced features unavailable: {e}")
        
        # Update data
        setattr(self.project_assumptions, field, value)
        self.project_assumptions.validate_all()  # Re-validate
        self.notify_data_changed("project_assumptions", self.project_assumptions)
        
        # Auto-save if enhanced service available
        try:
            # Check debounce timing
            if hasattr(self, '_last_auto_save_time'):
                if time.time() - self._last_auto_save_time < 2:  # Debounce auto-save
                    return

            if enhanced_service.persistence_service and self.client_profile.is_complete():
                project_id = self.client_profile.get_clean_company_name()
                enhanced_service.save_project_with_versioning(
                    project_id=project_id,
                    project_data={
                        'client_profile': self.client_profile.to_dict(),
                        'assumptions': self.project_assumptions.to_dict()
                    }
                )
                self._last_auto_save_time = time.time()
                print(f"Auto-saved project: {project_id}")
        except Exception as e:
            print(f"Auto-save failed: {e}")
        
        self.refresh()  # Refresh to update validation status
    
    def _on_load_preset(self, e):
        """Handle load preset action."""
        self.request_action("load_preset")

    def _on_show_history(self, e):
        """Handle show history action."""
        self.history_logger.info("HISTORY: Button clicked by user")

        try:
            # Log client profile status
            self.history_logger.debug(f"Client profile complete: {self.client_profile.is_complete()}")
            self.history_logger.debug(f"Client profile data: company='{self.client_profile.company_name}', "
                                    f"client='{self.client_profile.client_name}', "
                                    f"project='{self.client_profile.project_name}'")

            # Check if we have a valid client profile to get project ID
            if not self.client_profile.is_complete():
                self.history_logger.warning("ERROR: Client profile incomplete - cannot show history")
                self.show_error("Please complete client profile to view project history")
                return

            project_id = self.client_profile.get_clean_company_name()
            self.history_logger.info(f"HISTORY: Opening history for project ID: '{project_id}'")

            self._show_project_history_dialog(project_id)

        except Exception as e:
            self.history_logger.error(f"ERROR: Exception in _on_show_history: {e}")
            import traceback
            self.history_logger.error(f"Stack trace: {traceback.format_exc()}")
            self.show_error(f"Error opening history: {str(e)}")

    def _show_project_history_dialog(self, project_id: str):
        """Show project history dialog with loading state."""
        self.history_logger.info(f"DIALOG: Starting history dialog for project: '{project_id}'")

        # Show loading dialog first
        loading_dialog = ft.AlertDialog(
            title=ft.Text("Loading Project History"),
            content=ft.Container(
                content=ft.Column([
                    ft.ProgressRing(),
                    ft.Text("Loading project versions...", text_align=ft.TextAlign.CENTER)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                width=300,
                height=100,
                alignment=ft.alignment.center
            )
        )

        self.history_logger.debug("UI: Adding loading dialog to page overlay")
        self.page.overlay.append(loading_dialog)
        loading_dialog.open = True
        self.page.update()

        try:
            # Get persistence service
            self.history_logger.debug("SERVICE: Importing enhanced integration service")
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()
            self.history_logger.debug(f"SERVICE: Enhanced service obtained: {type(enhanced_service).__name__}")

            if not enhanced_service.persistence_service:
                self.history_logger.error("ERROR: Persistence service not available in enhanced service")
                loading_dialog.open = False
                self.page.update()
                self.show_error("Persistence service not available")
                return

            self.history_logger.debug(f"DATA: Querying project versions for: '{project_id}'")
            # Get project versions
            versions = enhanced_service.persistence_service.get_project_versions(project_id)
            self.history_logger.info(f"DATA: Found {len(versions)} version(s) for project '{project_id}'")

            # Log version details
            for i, version in enumerate(versions):
                self.history_logger.debug(f"   Version {version.get('version', 'unknown')}: "
                                        f"created={version.get('created_at', 'unknown')}, "
                                        f"size={version.get('file_size', 0)} bytes, "
                                        f"comment='{version.get('comment', 'no comment')}'")

            # Close loading dialog
            loading_dialog.open = False
            self.page.update()

            if not versions:
                self.history_logger.info("INFO: No versions found - showing notification to user")
                self.show_notification("No project history found for this project", ft.Colors.BLUE)
                return

            self.history_logger.info("UI: Creating history dialog with version list")
            # Create history dialog
            self._create_history_dialog(project_id, versions)

        except Exception as e:
            self.history_logger.error(f"ERROR: Exception in _show_project_history_dialog: {e}")
            import traceback
            self.history_logger.error(f"Stack trace: {traceback.format_exc()}")
            # Close loading dialog on error
            loading_dialog.open = False
            self.page.update()
            self.show_error(f"Error loading project history: {str(e)}")

    def _create_history_dialog(self, project_id: str, versions: list):
        """Create and show the project history dialog."""
        self.history_logger.info(f"UI: Creating history dialog for {len(versions)} versions")

        # Create version list items
        version_items = []
        for version_info in versions:
            version_num = version_info['version']
            created_at = version_info['created_at']
            comment = version_info.get('comment', 'No comment')
            file_size = version_info.get('file_size', 0)

            # Format date
            try:
                from datetime import datetime
                if isinstance(created_at, str):
                    date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                else:
                    date_obj = created_at
                formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")
            except:
                formatted_date = str(created_at)

            # Format file size
            if file_size > 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size} bytes"

            # Add visual indicator for current version
            is_current = version_num == versions[0]['version'] if versions else False

            version_item = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(
                                ft.Icons.STAR if is_current else ft.Icons.HISTORY,
                                color=ft.Colors.AMBER if is_current else ft.Colors.PURPLE_600
                            ),
                            ft.Column([
                                ft.Row([
                                    ft.Text(f"Version {version_num}",
                                           size=16, weight=ft.FontWeight.BOLD),
                                    ft.Container(
                                        content=ft.Text("CURRENT", size=10, color=ft.Colors.WHITE),
                                        bgcolor=ft.Colors.AMBER,
                                        padding=ft.padding.symmetric(horizontal=8, vertical=2),
                                        border_radius=10
                                    ) if is_current else ft.Container()
                                ]),
                                ft.Text(formatted_date, size=12, color=ft.Colors.GREY_600)
                            ], spacing=2),
                        ], alignment=ft.MainAxisAlignment.START, spacing=10),
                        ft.Container(height=5),
                        ft.Text(comment or "No comment", size=12, color=ft.Colors.GREY_700, italic=not comment),
                        ft.Row([
                            ft.Icon(ft.Icons.STORAGE, size=16, color=ft.Colors.GREY_500),
                            ft.Text(f"{size_str}", size=10, color=ft.Colors.GREY_500)
                        ], spacing=5),
                        ft.Container(height=10),
                        ft.Row([
                            ft.ElevatedButton(
                                "Restore This Version",
                                icon=ft.Icons.RESTORE,
                                on_click=lambda e, v=version_num: self._restore_version(project_id, v),
                                bgcolor=ft.Colors.PURPLE_600,
                                color=ft.Colors.WHITE,
                                disabled=is_current
                            ) if not is_current else ft.Text(
                                "This is the current version",
                                size=12,
                                color=ft.Colors.GREY_600,
                                italic=True
                            )
                        ], alignment=ft.MainAxisAlignment.END)
                    ]),
                    padding=15,
                    bgcolor=ft.Colors.AMBER_50 if is_current else None
                ),
                elevation=3 if is_current else 2
            )
            version_items.append(version_item)

        # Calculate statistics
        total_size = sum(v.get('file_size', 0) for v in versions)
        if total_size > 1024 * 1024:
            total_size_str = f"{total_size / (1024 * 1024):.1f} MB"
        elif total_size > 1024:
            total_size_str = f"{total_size / 1024:.1f} KB"
        else:
            total_size_str = f"{total_size} bytes"

        # Get date range
        if versions:
            oldest_date = versions[-1]['created_at']
            newest_date = versions[0]['created_at']
            try:
                from datetime import datetime
                if isinstance(oldest_date, str):
                    oldest_obj = datetime.fromisoformat(oldest_date.replace('Z', '+00:00'))
                    oldest_formatted = oldest_obj.strftime("%Y-%m-%d")
                else:
                    oldest_formatted = str(oldest_date)
            except:
                oldest_formatted = str(oldest_date)

        # Create dialog content
        dialog_content = ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.HISTORY, color=ft.Colors.PURPLE_600, size=24),
                ft.Text("Project History", size=20, weight=ft.FontWeight.BOLD)
            ]),
            ft.Divider(),
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.FOLDER, size=16, color=ft.Colors.BLUE_600),
                        ft.Text(f"Project: {project_id}", size=14, weight=ft.FontWeight.BOLD)
                    ], spacing=5),
                    ft.Row([
                        ft.Icon(ft.Icons.ANALYTICS, size=16, color=ft.Colors.GREEN_600),
                        ft.Text(f"{len(versions)} version(s) • Total size: {total_size_str}", size=12, color=ft.Colors.GREY_600)
                    ], spacing=5),
                    ft.Row([
                        ft.Icon(ft.Icons.DATE_RANGE, size=16, color=ft.Colors.ORANGE_600),
                        ft.Text(f"From: {oldest_formatted if versions else 'N/A'}", size=12, color=ft.Colors.GREY_600)
                    ], spacing=5) if versions else ft.Container()
                ], spacing=5),
                padding=10,
                bgcolor=ft.Colors.GREY_50,
                border_radius=8
            ),
            ft.Container(height=10),
            ft.Column(
                version_items,
                height=400,
                scroll=ft.ScrollMode.AUTO
            )
        ], width=650, height=550)

        # Create and show dialog
        self.history_dialog = ft.AlertDialog(
            title=ft.Text("Project History"),
            content=dialog_content,
            actions=[
                ft.TextButton("Close", on_click=self._close_history_dialog)
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )

        self.history_logger.info("UI: Adding history dialog to page overlay and showing")
        self.page.overlay.append(self.history_dialog)
        self.history_dialog.open = True
        self.page.update()
        self.history_logger.info("SUCCESS: History dialog displayed successfully")

    def _restore_version(self, project_id: str, version: int):
        """Restore a specific project version with confirmation and validation."""
        self.history_logger.info(f"RESTORE: User requested restore of version {version} for project '{project_id}'")

        # Show confirmation dialog first
        def confirm_restore(e):
            self.history_logger.info(f"RESTORE: User confirmed restore of version {version}")
            confirm_dialog.open = False
            self.page.update()
            self._perform_restore(project_id, version)

        def cancel_restore(e):
            self.history_logger.info(f"RESTORE: User cancelled restore of version {version}")
            confirm_dialog.open = False
            self.page.update()

        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Restore"),
            content=ft.Text(
                f"Are you sure you want to restore version {version}?\n\n"
                "This will replace your current client profile and project parameters "
                "with the data from the selected version. Any unsaved changes will be lost.",
                size=14
            ),
            actions=[
                ft.TextButton("Cancel", on_click=cancel_restore),
                ft.ElevatedButton(
                    "Restore",
                    icon=ft.Icons.RESTORE,
                    on_click=confirm_restore,
                    bgcolor=ft.Colors.PURPLE_600,
                    color=ft.Colors.WHITE
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )

        self.page.overlay.append(confirm_dialog)
        confirm_dialog.open = True
        self.page.update()

    def _perform_restore(self, project_id: str, version: int):
        """Perform the actual restore operation with loading state."""
        self.history_logger.info(f"RESTORE: Starting restore operation for version {version} of project '{project_id}'")

        # Show loading dialog
        loading_dialog = ft.AlertDialog(
            title=ft.Text("Restoring Version"),
            content=ft.Container(
                content=ft.Column([
                    ft.ProgressRing(),
                    ft.Text(f"Restoring version {version}...", text_align=ft.TextAlign.CENTER)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                width=300,
                height=100,
                alignment=ft.alignment.center
            )
        )

        self.page.overlay.append(loading_dialog)
        loading_dialog.open = True
        self.page.update()

        try:
            # Get persistence service
            self.history_logger.debug("SERVICE: Getting enhanced integration service for restore")
            from services.enhanced_integration_service import get_integration_service
            enhanced_service = get_integration_service()

            if not enhanced_service.persistence_service:
                self.history_logger.error("ERROR: Persistence service not available for restore")
                loading_dialog.open = False
                self.page.update()
                self.show_error("Persistence service not available")
                return

            self.history_logger.debug(f"DATA: Loading project data for version {version}")
            # Load the specific version
            project_data = enhanced_service.persistence_service.load_project(project_id, version)

            if not project_data:
                self.history_logger.error(f"ERROR: Could not load project data for version {version}")
                loading_dialog.open = False
                self.page.update()
                self.show_error(f"Could not load version {version}. The version may be corrupted or no longer available.")
                return

            self.history_logger.info(f"SUCCESS: Successfully loaded project data for version {version}")

            # Validate the loaded data
            validation_errors = []

            # Update current data with validation
            if project_data.client_profile:
                try:
                    self.history_logger.debug("RESTORE: Restoring client profile data")
                    from models.client_profile import ClientProfile
                    self.client_profile = ClientProfile.from_dict(project_data.client_profile)

                    # Validate client profile
                    client_errors = self.client_profile.validate()
                    if client_errors:
                        self.history_logger.warning(f"WARNING: Client profile validation errors: {client_errors}")
                        validation_errors.extend([f"Client Profile: {error}" for error in client_errors.values()])

                    if self.client_form:
                        self.client_form.update_data(self.client_profile)
                        self.history_logger.debug("SUCCESS: Client form updated with restored data")
                except Exception as e:
                    self.history_logger.error(f"ERROR: Error restoring client profile: {e}")
                    validation_errors.append(f"Error restoring client profile: {str(e)}")

            if project_data.project_assumptions:
                try:
                    self.history_logger.debug("RESTORE: Restoring project assumptions data")
                    from models.project_assumptions import EnhancedProjectAssumptions
                    self.project_assumptions = EnhancedProjectAssumptions.from_dict(project_data.project_assumptions)

                    # Validate project assumptions
                    assumption_errors = self.project_assumptions.validate_all()
                    if assumption_errors:
                        self.history_logger.warning(f"WARNING: Project assumptions validation errors: {assumption_errors}")
                        validation_errors.extend([f"Project Parameters: {error}" for error in assumption_errors.values()])

                    if self.params_form:
                        self.params_form.update_data(self.project_assumptions)
                        self.history_logger.debug("SUCCESS: Parameters form updated with restored data")
                except Exception as e:
                    self.history_logger.error(f"ERROR: Error restoring project assumptions: {e}")
                    validation_errors.append(f"Error restoring project assumptions: {str(e)}")

            # Close loading dialog
            loading_dialog.open = False
            self.page.update()

            # Close history dialog and refresh view
            self._close_history_dialog(None)
            self.refresh()

            # Show results
            if validation_errors:
                self.history_logger.warning(f"WARNING: Version {version} restored with {len(validation_errors)} warnings")
                error_message = f"Version {version} restored with warnings:\n\n" + "\n".join(validation_errors)
                self.show_notification(error_message, ft.Colors.ORANGE)
            else:
                self.history_logger.info(f"SUCCESS: Version {version} restored successfully without errors")
                self.show_notification(f"Successfully restored version {version}", ft.Colors.GREEN)

        except Exception as e:
            self.history_logger.error(f"ERROR: Exception during restore operation: {e}")
            import traceback
            self.history_logger.error(f"Stack trace: {traceback.format_exc()}")
            # Close loading dialog on error
            loading_dialog.open = False
            self.page.update()
            self.show_error(f"Error restoring version {version}: {str(e)}")

    def _close_history_dialog(self, e):
        """Close the history dialog."""
        self.history_logger.info("DIALOG: Closing history dialog")
        if hasattr(self, 'history_dialog'):
            self.history_dialog.open = False
            self.page.update()
            self.history_logger.debug("SUCCESS: History dialog closed successfully")
    
    def _on_save_config(self, e):
        """Handle save configuration action."""
        # Validate before saving
        if not self.client_profile.is_complete():
            self.show_error("Please complete client profile before saving")
            return
        
        if not self.project_assumptions.is_validated:
            self.show_error("Please fix validation errors before saving")
            return
        
        self.request_action("save_configuration", {
            "client_profile": self.client_profile.to_dict(),
            "project_assumptions": self.project_assumptions.to_dict()
        })
    
    def _on_run_model(self, e):
        """Handle run model action."""
        # Validate before running
        if not self.client_profile.is_complete():
            self.show_error("Please complete client profile before running model")
            return
        
        if not self.project_assumptions.is_validated:
            self.show_error("Please fix validation errors before running model")
            return
        
        self.request_action("run_financial_model", {
            "assumptions": self.project_assumptions.to_dict()
        })
    
    def _on_comprehensive_analysis(self, e):
        """Handle comprehensive analysis action."""
        # Validate before running
        if not self.client_profile.is_complete():
            self.show_error("Please complete client profile before running comprehensive analysis")
            return
        
        if not self.project_assumptions.is_validated:
            self.show_error("Please fix validation errors before running comprehensive analysis")
            return
        
        self.request_action("run_comprehensive_analysis", {
            "client_profile": self.client_profile.to_dict(),
            "assumptions": self.project_assumptions.to_dict()
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with new data."""
        if "client_profile" in data:
            self.client_profile = ClientProfile.from_dict(data["client_profile"])
            if self.client_form:
                self.client_form.update_data(self.client_profile)
        
        if "project_assumptions" in data:
            self.project_assumptions = EnhancedProjectAssumptions.from_dict(data["project_assumptions"])
            if self.params_form:
                self.params_form.update_data(self.project_assumptions)
        
        self.refresh()
    
    def get_client_profile(self) -> ClientProfile:
        """Get current client profile."""
        return self.client_profile
    
    def get_project_assumptions(self) -> EnhancedProjectAssumptions:
        """Get current project assumptions."""
        return self.project_assumptions
    
    def set_client_profile(self, client_profile: ClientProfile):
        """Set client profile."""
        self.client_profile = client_profile
        if self.client_form:
            self.client_form.update_data(client_profile)
        self.refresh()
    
    def set_project_assumptions(self, assumptions: EnhancedProjectAssumptions):
        """Set project assumptions."""
        self.project_assumptions = assumptions
        if self.params_form:
            self.params_form.update_data(assumptions)
        self.refresh()
    
    def validate_all(self) -> bool:
        """Validate all form data."""
        client_valid = self.client_profile.is_complete()
        params_valid = self.project_assumptions.is_validated
        
        return client_valid and params_valid
